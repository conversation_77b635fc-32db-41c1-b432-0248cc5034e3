/**
 * Test Conflict Detection with Real Sims 4 Mods
 * 
 * Tests the conflict detection system with the user's actual Sims 4 mods folder
 * to provide realistic conflict detection results.
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package } from '@s4tk/models';
import { ContentAnalysisService } from '../services/analysis/content/ContentAnalysisService';
import { ModCollectionAnalyzer } from '../services/analysis/conflicts/ModCollectionAnalyzer';

/**
 * Test conflict detection with real Sims 4 mods
 * @param fullCollection - If true, tests entire collection (may be slow)
 */
async function testRealWorldConflicts(fullCollection: boolean = false) {
    console.log('🔍 Testing Conflict Detection with Real Sims 4 Mods');
    console.log('==================================================\n');

    const contentAnalysisService = new ContentAnalysisService();
    
    // User's actual Sims 4 mods folder
    const realModsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    console.log(`📂 Checking real mods folder: ${realModsPath}`);
    
    if (!fs.existsSync(realModsPath)) {
        console.log('❌ Real mods folder not found. Falling back to test assets...\n');
        await testWithAssets();
        return;
    }

    // Get all .package files recursively
    const modFiles = getAllPackageFiles(realModsPath);
    
    if (modFiles.length === 0) {
        console.log('❌ No .package files found in real mods folder');
        return;
    }

    console.log(`📦 Found ${modFiles.length} mod files in real collection`);

    // Determine how many mods to test
    let testCount: number;
    if (fullCollection) {
        testCount = modFiles.length;
        console.log(`🔍 Testing FULL collection (${testCount} mods) - this may take a while...\n`);
    } else {
        // Smart sampling based on collection size
        testCount = Math.min(modFiles.length, 100); // Test up to 100 mods
        if (modFiles.length > 500) {
            testCount = Math.min(modFiles.length, 200); // For large collections, test up to 200
        }
        console.log(`🔍 Testing with ${testCount} mods (${(testCount/modFiles.length*100).toFixed(1)}% of collection)...\n`);
        console.log(`💡 To test full collection, run: testRealWorldConflicts(true)\n`);
    }

    const testMods = modFiles.slice(0, testCount);

    // Step 1: Analyze individual mods
    console.log('🔍 Step 1: Analyzing individual mods...');
    const modAnalyses = new Map();
    const packages = new Map();
    let analysisTime = 0;
    let successCount = 0;

    for (const modPath of testMods) {
        const modFile = path.basename(modPath);
        
        try {
            const buffer = fs.readFileSync(modPath);
            const startTime = performance.now();
            
            // Analyze content
            const analysis = await contentAnalysisService.analyzeModContent(buffer, modFile);
            modAnalyses.set(modFile, analysis);
            
            // Parse package for conflict detection
            const s4tkPackage = Package.from(buffer);
            packages.set(modFile, s4tkPackage);
            
            analysisTime += performance.now() - startTime;
            successCount++;
            
            console.log(`   ✅ ${modFile} - ${analysis.contentType} (${analysis.totalItems} items)`);
            
        } catch (error) {
            console.error(`   ❌ Error analyzing ${modFile}:`, error.message);
        }
    }

    console.log(`\n📊 Individual analysis complete: ${successCount}/${testMods.length} successful, ${analysisTime.toFixed(2)}ms total\n`);

    if (successCount === 0) {
        console.log('❌ No mods could be analyzed successfully');
        return;
    }

    // Step 2: Collection-level conflict analysis
    console.log('🔍 Step 2: Analyzing real mod collection for conflicts...');
    const collectionStartTime = performance.now();
    
    try {
        const collectionAnalysis = await ModCollectionAnalyzer.analyzeCollection(
            modAnalyses,
            packages
        );
        
        const collectionTime = performance.now() - collectionStartTime;
        
        // Display comprehensive results
        displayDetailedResults(collectionAnalysis, collectionTime, analysisTime, testMods.length, successCount);
        
    } catch (error) {
        console.error('❌ Error during collection analysis:', error);
    }
}

/**
 * Recursively gets all .package files from a directory
 */
function getAllPackageFiles(dirPath: string): string[] {
    const packageFiles: string[] = [];
    
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Recursively search subdirectories
                packageFiles.push(...getAllPackageFiles(fullPath));
            } else if (item.endsWith('.package')) {
                packageFiles.push(fullPath);
            }
        }
    } catch (error) {
        console.warn(`Warning: Could not read directory ${dirPath}:`, error.message);
    }
    
    return packageFiles;
}

/**
 * Displays detailed conflict detection results
 */
function displayDetailedResults(
    collectionAnalysis: any,
    collectionTime: number,
    analysisTime: number,
    totalMods: number,
    successCount: number
) {
    console.log('\n📊 REAL-WORLD CONFLICT DETECTION RESULTS');
    console.log('========================================');
    console.log(`Total mods in collection: ${totalMods}`);
    console.log(`Successfully analyzed: ${successCount}`);
    console.log(`Total conflicts found: ${collectionAnalysis.totalConflicts}`);
    console.log(`Analysis time: ${collectionTime.toFixed(2)}ms`);
    
    // Conflict summary
    console.log('\n🚨 Conflict Summary:');
    console.log(`   CAS semantic conflicts: ${collectionAnalysis.resourceConflicts.filter((c: any) => c.conflictType === 'cas_semantic_conflict').length}`);
    console.log(`   Object functional conflicts: ${collectionAnalysis.resourceConflicts.filter((c: any) => c.conflictType === 'object_functional_conflict').length}`);
    console.log(`   Gameplay system conflicts: ${collectionAnalysis.resourceConflicts.filter((c: any) => c.conflictType === 'gameplay_system_conflict').length}`);
    console.log(`   Exact duplicates: ${collectionAnalysis.conflictSummary.byType.exactDuplicates}`);
    console.log(`   Override conflicts: ${collectionAnalysis.conflictSummary.byType.overrideConflicts}`);
    console.log(`   Known conflicts: ${collectionAnalysis.conflictSummary.byType.knownConflicts}`);
    
    // Severity breakdown
    console.log('\n⚠️  Severity Breakdown:');
    console.log(`   Critical: ${collectionAnalysis.conflictSummary.bySeverity.critical}`);
    console.log(`   High: ${collectionAnalysis.conflictSummary.bySeverity.high}`);
    console.log(`   Medium: ${collectionAnalysis.conflictSummary.bySeverity.medium}`);
    console.log(`   Low: ${collectionAnalysis.conflictSummary.bySeverity.low}`);
    
    // Show critical conflicts first
    const criticalConflicts = collectionAnalysis.resourceConflicts.filter((c: any) => c.severity === 'critical');
    if (criticalConflicts.length > 0) {
        console.log('\n🚨 CRITICAL CONFLICTS (Immediate Attention Required):');
        criticalConflicts.forEach((conflict: any, index: number) => {
            console.log(`   ${index + 1}. ${conflict.description}`);
            console.log(`      Affected mods: ${conflict.affectedMods.slice(0, 3).join(', ')}${conflict.affectedMods.length > 3 ? '...' : ''}`);
            console.log(`      Resolution: ${conflict.resolution.description}`);
            console.log('');
        });
    }
    
    // Show high priority conflicts
    const highConflicts = collectionAnalysis.resourceConflicts.filter((c: any) => c.severity === 'high');
    if (highConflicts.length > 0) {
        console.log('\n⚠️  HIGH PRIORITY CONFLICTS:');
        highConflicts.slice(0, 5).forEach((conflict: any, index: number) => {
            console.log(`   ${index + 1}. ${conflict.description}`);
            console.log(`      Type: ${conflict.conflictType}`);
            console.log(`      Affected mods: ${conflict.affectedMods.slice(0, 2).join(', ')}${conflict.affectedMods.length > 2 ? '...' : ''}`);
            console.log('');
        });
    }
    
    // Known conflicts
    if (collectionAnalysis.knownConflicts.length > 0) {
        console.log('\n🗃️  KNOWN CONFLICTS:');
        collectionAnalysis.knownConflicts.forEach((conflict: any, index: number) => {
            console.log(`   ${index + 1}. ${conflict.title} (${conflict.severity})`);
            console.log(`      ${conflict.description}`);
            console.log(`      Affected mods: ${conflict.affectedMods.join(', ')}`);
            console.log(`      Resolution: ${conflict.resolution}`);
            console.log('');
        });
    }
    
    // Recommendations
    if (collectionAnalysis.recommendations.length > 0) {
        console.log('\n💡 RECOMMENDATIONS:');
        collectionAnalysis.recommendations.forEach((rec: any, index: number) => {
            console.log(`   ${index + 1}. ${rec.title} (${rec.priority})`);
            console.log(`      ${rec.description}`);
            if (rec.affectedMods.length > 0) {
                console.log(`      Affected: ${rec.affectedMods.slice(0, 3).join(', ')}${rec.affectedMods.length > 3 ? '...' : ''}`);
            }
            console.log('');
        });
    }
    
    // Performance assessment
    console.log('\n⚡ Performance Assessment:');
    const avgAnalysisTime = analysisTime / successCount;
    console.log(`   Individual mod analysis: ${avgAnalysisTime.toFixed(2)}ms average`);
    console.log(`   Collection analysis: ${collectionTime.toFixed(2)}ms`);
    console.log(`   Total time: ${(analysisTime + collectionTime).toFixed(2)}ms`);
    
    // Final assessment
    console.log('\n🎯 Real-World Assessment:');
    console.log(`   Gameplay-affecting conflicts: ${collectionAnalysis.resourceConflicts.filter((c: any) => c.conflictType.includes('semantic') || c.conflictType.includes('functional')).length > 0 ? 'DETECTED' : 'NONE FOUND'}`);
    console.log(`   Known problematic patterns: ${collectionAnalysis.knownConflicts.length > 0 ? 'DETECTED' : 'NONE FOUND'}`);
    console.log(`   Organization suggestions: ${collectionAnalysis.organizationSuggestions.length > 0 ? 'PROVIDED' : 'NONE'}`);

    // Show what known conflicts were actually found in real mods
    if (collectionAnalysis.knownConflicts.length > 0) {
        console.log('\n🚨 ACTUAL Known Conflicts in Your Collection:');
        collectionAnalysis.knownConflicts.forEach((conflict: any, index: number) => {
            console.log(`   ${index + 1}. ${conflict.title}: ${conflict.affectedMods.join(', ')}`);
        });
    } else {
        console.log('\n✅ No known problematic mod combinations detected in your collection');
    }

    if (collectionAnalysis.totalConflicts > 0) {
        console.log('\n🎉 Conflict detection found real issues that need attention!');
        if (criticalConflicts.length > 0) {
            console.log('⚠️  IMPORTANT: Address critical conflicts first to prevent game issues.');
        }
    } else {
        console.log('\n✅ No gameplay-affecting conflicts detected - your mod collection is well-organized!');
        console.log('💡 The system only flags conflicts that actually break gameplay, not harmless variety.');
    }
}

/**
 * Fallback to test with assets if real mods folder not found
 */
async function testWithAssets() {
    console.log('🔍 Testing with project assets folder...');
    const assetsPath = path.join(__dirname, '../../assets');
    
    if (!fs.existsSync(assetsPath)) {
        console.log('❌ Assets folder also not found');
        return;
    }
    
    // Run the original test logic with assets
    console.log('📦 Using test assets for conflict detection demo...');
    // This would call the original test function
}

// Run the test
if (require.main === module) {
    testRealWorldConflicts().catch(console.error);
}

export { testRealWorldConflicts };
