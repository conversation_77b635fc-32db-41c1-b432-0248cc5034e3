{"casConflicts": {"hairConflicts": {"rule": "same_age_gender_category", "description": "Multiple hair mods for the same age/gender combination", "severity": "medium", "resolution": "user_choice", "autoFix": false, "categories": ["hair"]}, "clothingConflicts": {"rule": "same_age_gender_bodyregion", "description": "Multiple clothing items for the same body region", "severity": "low", "resolution": "keep_all", "autoFix": false, "categories": ["tops", "bottoms", "fullbody", "shoes"]}, "accessoryConflicts": {"rule": "same_age_gender_category", "description": "Multiple accessories of the same type", "severity": "low", "resolution": "keep_all", "autoFix": false, "categories": ["accessories", "jewelry", "glasses"]}}, "objectConflicts": {"functionalConflicts": {"rule": "same_function_room", "description": "Multiple objects with the same function in the same room", "severity": "medium", "resolution": "user_choice", "autoFix": false, "functions": ["seating", "sleeping", "cooking", "hygiene"]}, "catalogConflicts": {"rule": "same_catalog_placement", "description": "Objects that override the same catalog placement", "severity": "high", "resolution": "keep_latest", "autoFix": true}}, "scriptConflicts": {"moduleConflicts": {"rule": "same_python_module", "description": "Multiple script mods modifying the same Python modules", "severity": "critical", "resolution": "user_choice", "autoFix": false}, "gameplayConflicts": {"rule": "same_gameplay_system", "description": "Multiple mods modifying the same gameplay systems", "severity": "high", "resolution": "user_choice", "autoFix": false, "systems": ["pregnancy", "relationships", "careers", "traits"]}}, "knownConflicts": {"mcccConflicts": {"patterns": ["mc_", "mccommand", "mccc"], "type": "version_conflict", "description": "Multiple MCCC versions detected", "severity": "critical", "resolution": "keep_latest", "autoFix": false}, "uiCheatsConflicts": {"patterns": ["ui_cheat", "uicheat", "ui-cheat"], "type": "duplicate_functionality", "description": "Multiple UI Cheats Extension mods", "severity": "high", "resolution": "keep_one", "autoFix": false}, "whimsConflicts": {"patterns": ["wonderfulwhims", "wickedwhims"], "type": "mutually_exclusive", "description": "Wonderful Whims and Wicked Whims cannot coexist", "severity": "critical", "resolution": "choose_one", "autoFix": false}, "basementalConflicts": {"patterns": ["basemental"], "type": "version_compatibility", "description": "Multiple Basemental mods may have version conflicts", "severity": "medium", "resolution": "check_versions", "autoFix": false}, "pregnancyConflicts": {"patterns": ["pregnancy", "woohoo", "risky"], "type": "gameplay_overlap", "description": "Multiple pregnancy/woohoo overhaul mods", "severity": "high", "resolution": "choose_one", "autoFix": false}, "sliceOfLifeConflicts": {"patterns": ["sliceoflife", "sol_"], "type": "comprehensive_overhaul", "description": "Slice of Life may conflict with other life simulation mods", "severity": "medium", "resolution": "check_compatibility", "autoFix": false}, "lot51Conflicts": {"patterns": ["lot51"], "type": "dependency_missing", "description": "Lot51 mods require core library", "severity": "high", "resolution": "install_dependency", "autoFix": false, "requiredMods": ["lot51_core", "lot51_library"]}, "outdatedModConflicts": {"patterns": ["old", "legacy", "deprecated", "v1.0", "beta"], "type": "version_outdated", "description": "Potentially outdated mods detected", "severity": "medium", "resolution": "update_mods", "autoFix": false}, "duplicateAuthorConflicts": {"patterns": [], "type": "author_duplicate", "description": "Multiple mods from same author may conflict", "severity": "low", "resolution": "check_compatibility", "autoFix": false}}, "resourceConflicts": {"exactDuplicates": {"rule": "identical_tgi", "description": "Exact duplicate resources across mods", "severity": "medium", "resolution": "keep_latest", "autoFix": true}, "overrideConflicts": {"rule": "base_game_override", "description": "Multiple mods overriding same base game resource", "severity": "high", "resolution": "user_choice", "autoFix": false}, "hashCollisions": {"rule": "hash_collision_detection", "description": "Potential hash collisions detected", "severity": "medium", "resolution": "investigate", "autoFix": false}}, "severityLevels": {"low": {"color": "yellow", "action": "monitor", "description": "Minor conflicts that rarely cause issues"}, "medium": {"color": "orange", "action": "review", "description": "Moderate conflicts that may cause issues"}, "high": {"color": "red", "action": "resolve", "description": "Significant conflicts that likely cause issues"}, "critical": {"color": "darkred", "action": "immediate", "description": "Critical conflicts that will cause crashes or major issues"}}, "resolutionStrategies": {"keep_latest": "Keep the most recently modified version", "keep_all": "Keep all versions (usually safe for CAS content)", "user_choice": "User must manually choose which to keep", "choose_one": "User must choose only one from the conflicting set", "keep_one": "Keep only one, remove others", "install_dependency": "Install required dependency mods", "update_mods": "Update to newer versions", "check_compatibility": "Manually verify compatibility", "check_versions": "Ensure compatible versions are installed", "investigate": "Manual investigation required", "monitor": "Monitor for issues but no immediate action needed"}}