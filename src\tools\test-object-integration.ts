/**
 * Test Object Analysis Integration
 * 
 * Comprehensive test to validate object content analysis integration
 * with the existing ContentAnalysisService and real-world mod files.
 */

import * as fs from 'fs';
import * as path from 'path';
import { ContentAnalysisService } from '../services/analysis/content/ContentAnalysisService';

/**
 * Test object analysis integration with real mod files
 */
async function testObjectIntegration() {
    console.log('🧪 Testing Object Analysis Integration');
    console.log('=====================================\n');

    const contentAnalysisService = new ContentAnalysisService();
    
    // Test with the real-world test framework
    const testModsPath = path.join(__dirname, '../../tests/assets');
    
    if (!fs.existsSync(testModsPath)) {
        console.log('❌ Test assets directory not found');
        console.log('📝 To test with real mods, place .package files in tests/assets/\n');
        return;
    }

    const modFiles = fs.readdirSync(testModsPath)
        .filter(file => file.endsWith('.package'));

    if (modFiles.length === 0) {
        console.log('❌ No .package files found in test assets');
        console.log('📝 To test with real mods, place .package files in tests/assets/\n');
        return;
    }

    console.log(`📦 Testing with ${modFiles.length} mod files:\n`);

    let totalMods = 0;
    let modsWithObjects = 0;
    let totalObjects = 0;
    let totalAnalysisTime = 0;

    const categoryStats = {
        seating: 0,
        surfaces: 0,
        storage: 0,
        lighting: 0,
        decorative: 0,
        appliances: 0,
        plumbing: 0,
        electronics: 0,
        plants: 0,
        misc: 0
    };

    const roomStats = {
        living: 0,
        bedroom: 0,
        kitchen: 0,
        bathroom: 0,
        dining: 0,
        outdoor: 0,
        office: 0,
        kids: 0,
        any: 0
    };

    for (const modFile of modFiles) {
        const modPath = path.join(testModsPath, modFile);
        
        try {
            const buffer = fs.readFileSync(modPath);
            const startTime = performance.now();
            
            const analysis = await contentAnalysisService.analyzeModContent(buffer, modFile);
            
            const analysisTime = performance.now() - startTime;
            totalAnalysisTime += analysisTime;
            totalMods++;

            if (analysis.objectContent.totalItems > 0) {
                modsWithObjects++;
                totalObjects += analysis.objectContent.totalItems;
                
                console.log(`🏠 ${modFile}`);
                console.log(`   Objects: ${analysis.objectContent.totalItems}`);
                console.log(`   Content Type: ${analysis.contentType}`);
                console.log(`   Confidence: ${analysis.confidence}%`);
                
                // Aggregate category stats
                categoryStats.seating += analysis.objectContent.seating;
                categoryStats.surfaces += analysis.objectContent.surfaces;
                categoryStats.storage += analysis.objectContent.storage;
                categoryStats.lighting += analysis.objectContent.lighting;
                categoryStats.decorative += analysis.objectContent.decorative;
                categoryStats.appliances += analysis.objectContent.appliances;
                categoryStats.plumbing += analysis.objectContent.plumbing;
                categoryStats.electronics += analysis.objectContent.electronics;
                categoryStats.plants += analysis.objectContent.plants;
                categoryStats.misc += analysis.objectContent.misc;

                // Aggregate room stats
                roomStats.living += analysis.objectContent.roomTypes.living;
                roomStats.bedroom += analysis.objectContent.roomTypes.bedroom;
                roomStats.kitchen += analysis.objectContent.roomTypes.kitchen;
                roomStats.bathroom += analysis.objectContent.roomTypes.bathroom;
                roomStats.dining += analysis.objectContent.roomTypes.dining;
                roomStats.outdoor += analysis.objectContent.roomTypes.outdoor;
                roomStats.office += analysis.objectContent.roomTypes.office;
                roomStats.kids += analysis.objectContent.roomTypes.kids;
                roomStats.any += analysis.objectContent.roomTypes.any;

                // Show sample items
                if (analysis.objectContent.items.length > 0) {
                    console.log(`   Sample Items:`);
                    analysis.objectContent.items.slice(0, 2).forEach((item, index) => {
                        console.log(`      ${index + 1}. ${item.description}`);
                    });
                }
                
                console.log(`   Analysis Time: ${analysisTime.toFixed(2)}ms\n`);
            }

        } catch (error) {
            console.error(`❌ Error analyzing ${modFile}:`, error.message);
        }
    }

    // Summary Report
    console.log('📊 OBJECT ANALYSIS INTEGRATION SUMMARY');
    console.log('======================================');
    console.log(`Total mods analyzed: ${totalMods}`);
    console.log(`Mods with objects: ${modsWithObjects} (${(modsWithObjects/totalMods*100).toFixed(1)}%)`);
    console.log(`Total objects found: ${totalObjects}`);
    console.log(`Average analysis time: ${(totalAnalysisTime/totalMods).toFixed(2)}ms`);
    
    if (totalObjects > 0) {
        console.log(`\n📊 Category Distribution:`);
        Object.entries(categoryStats).forEach(([category, count]) => {
            if (count > 0) {
                console.log(`   ${category}: ${count} (${(count/totalObjects*100).toFixed(1)}%)`);
            }
        });

        console.log(`\n🏠 Room Distribution:`);
        Object.entries(roomStats).forEach(([room, count]) => {
            if (count > 0) {
                console.log(`   ${room}: ${count} (${(count/totalObjects*100).toFixed(1)}%)`);
            }
        });
    }

    // Validation
    console.log(`\n✅ VALIDATION RESULTS:`);
    console.log(`   Object analysis integration: ${totalObjects > 0 ? 'WORKING' : 'NO OBJECTS FOUND'}`);
    console.log(`   Performance: ${totalAnalysisTime/totalMods < 100 ? 'EXCELLENT' : 'NEEDS OPTIMIZATION'}`);
    console.log(`   Error rate: ${((totalMods - totalMods)/totalMods*100).toFixed(1)}%`);
    
    if (totalObjects === 0) {
        console.log(`\n💡 Note: No objects found in test mods. This is expected if testing with CAS-only mods.`);
        console.log(`   To test object analysis, use mods containing furniture, decorations, or build items.`);
    } else {
        console.log(`\n🎉 Object analysis is working correctly!`);
    }
}

/**
 * Test object analysis with simulated object resources
 */
async function testWithSimulatedObjects() {
    console.log('\n🧪 Testing with Simulated Object Resources');
    console.log('==========================================\n');

    // Test the object categorization logic directly
    const { ObjectCategoryDetector } = await import('../services/analysis/specialized/objects');
    
    const testCases = [
        { filename: 'luxury_sofa_livingroom.package', expectedCategory: 'seating', expectedRoom: 'living_room' },
        { filename: 'kitchen_island_granite.package', expectedCategory: 'surfaces', expectedRoom: 'kitchen' },
        { filename: 'bedroom_dresser_modern.package', expectedCategory: 'storage', expectedRoom: 'bedroom' },
        { filename: 'bathroom_shower_premium.package', expectedCategory: 'plumbing', expectedRoom: 'bathroom' },
        { filename: 'dining_chandelier_crystal.package', expectedCategory: 'lighting', expectedRoom: 'any_room' },
        { filename: 'office_computer_gaming.package', expectedCategory: 'electronics', expectedRoom: 'office' },
        { filename: 'outdoor_grill_bbq.package', expectedCategory: 'appliances', expectedRoom: 'outdoor' },
        { filename: 'kids_toybox_colorful.package', expectedCategory: 'storage', expectedRoom: 'kids_room' },
        { filename: 'decorative_vase_ceramic.package', expectedCategory: 'decorative', expectedRoom: 'any_room' },
        { filename: 'garden_plant_tropical.package', expectedCategory: 'plants', expectedRoom: 'any_room' }
    ];

    let correctCategories = 0;
    let correctRooms = 0;

    console.log('🔍 Testing categorization accuracy:\n');

    for (const testCase of testCases) {
        const category = ObjectCategoryDetector.detectCategoryFromFilename(testCase.filename);
        const rooms = ObjectCategoryDetector.detectRoomAssignment(category, testCase.filename);
        
        const categoryCorrect = category === testCase.expectedCategory;
        const roomCorrect = rooms.includes(testCase.expectedRoom as any);
        
        if (categoryCorrect) correctCategories++;
        if (roomCorrect) correctRooms++;
        
        console.log(`📦 ${testCase.filename}`);
        console.log(`   Category: ${category} ${categoryCorrect ? '✅' : '❌'} (expected: ${testCase.expectedCategory})`);
        console.log(`   Rooms: ${rooms.join(', ')} ${roomCorrect ? '✅' : '❌'} (expected: ${testCase.expectedRoom})`);
        console.log('');
    }

    console.log('📊 CATEGORIZATION ACCURACY:');
    console.log(`   Category accuracy: ${correctCategories}/${testCases.length} (${(correctCategories/testCases.length*100).toFixed(1)}%)`);
    console.log(`   Room accuracy: ${correctRooms}/${testCases.length} (${(correctRooms/testCases.length*100).toFixed(1)}%)`);
    
    const overallAccuracy = (correctCategories + correctRooms) / (testCases.length * 2) * 100;
    console.log(`   Overall accuracy: ${overallAccuracy.toFixed(1)}%`);
    
    if (overallAccuracy >= 90) {
        console.log('🎉 Excellent categorization accuracy!');
    } else if (overallAccuracy >= 70) {
        console.log('✅ Good categorization accuracy!');
    } else {
        console.log('⚠️  Categorization accuracy needs improvement');
    }
}

// Run the tests
if (require.main === module) {
    testObjectIntegration()
        .then(() => testWithSimulatedObjects())
        .catch(console.error);
}

export { testObjectIntegration };
