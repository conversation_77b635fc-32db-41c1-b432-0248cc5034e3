/**
 * Resource Conflict Detector
 * 
 * Implements actual conflict detection algorithms for resource ID collisions,
 * duplicate resources, and cross-mod conflicts. This replaces the TODO placeholders
 * in ContentAnalysisService with real working algorithms.
 * 
 * Addresses Reddit #1 request: "Flag conflicting mods"
 */

import { Package } from '@s4tk/models';
import { ModContentAnalysis } from '../content/ContentAnalysisService';
import { ConflictSeverity, ConflictType } from '../specialized/cas/types';

/**
 * Resource identifier for conflict detection
 */
export interface ResourceIdentifier {
    type: number;
    group: number;
    instance: number;
    modFile: string;
    resourceIndex: number;
}

/**
 * Detected resource conflict
 */
export interface ResourceConflict {
    conflictType: ResourceConflictType;
    severity: ConflictSeverity;
    description: string;
    affectedMods: string[];
    affectedResources: ResourceIdentifier[];
    resolution: ConflictResolution;
    autoFixAvailable: boolean;
}

/**
 * Types of resource conflicts
 */
export enum ResourceConflictType {
    EXACT_DUPLICATE = 'exact_duplicate',        // Same TGI in multiple mods
    OVERRIDE_CONFLICT = 'override_conflict',    // Multiple mods override same resource
    HASH_COLLISION = 'hash_collision',          // Different content, same hash
    DEPENDENCY_MISSING = 'dependency_missing',  // Required resource not found
    CIRCULAR_DEPENDENCY = 'circular_dependency' // Mods depend on each other
}

/**
 * Conflict resolution strategies
 */
export interface ConflictResolution {
    strategy: ResolutionStrategy;
    description: string;
    steps: string[];
    automaticFix: boolean;
}

export enum ResolutionStrategy {
    KEEP_LATEST = 'keep_latest',
    KEEP_HIGHEST_PRIORITY = 'keep_highest_priority',
    MERGE_COMPATIBLE = 'merge_compatible',
    USER_CHOICE = 'user_choice',
    REMOVE_CONFLICTING = 'remove_conflicting'
}

/**
 * Main Resource Conflict Detector
 */
export class ResourceConflictDetector {
    
    /**
     * Analyzes multiple mods for resource conflicts
     */
    public static detectConflicts(
        modAnalyses: Map<string, ModContentAnalysis>,
        packages: Map<string, Package>
    ): ResourceConflict[] {
        const conflicts: ResourceConflict[] = [];
        
        console.log(`[ResourceConflictDetector] Analyzing ${modAnalyses.size} mods for conflicts...`);
        
        // Build resource map across all mods
        const resourceMap = this.buildResourceMap(packages);
        
        // Detect exact duplicates
        conflicts.push(...this.detectExactDuplicates(resourceMap));
        
        // Detect override conflicts
        conflicts.push(...this.detectOverrideConflicts(resourceMap));
        
        // Detect hash collisions
        conflicts.push(...this.detectHashCollisions(resourceMap));
        
        // Detect dependency issues
        conflicts.push(...this.detectDependencyConflicts(resourceMap, modAnalyses));
        
        console.log(`[ResourceConflictDetector] Found ${conflicts.length} conflicts`);
        
        return conflicts;
    }
    
    /**
     * Builds a comprehensive map of all resources across all mods
     */
    private static buildResourceMap(packages: Map<string, Package>): Map<string, ResourceIdentifier[]> {
        const resourceMap = new Map<string, ResourceIdentifier[]>();
        
        for (const [modFile, s4tkPackage] of packages) {
            let resourceIndex = 0;
            
            for (const entry of s4tkPackage.entries.values()) {
                const tgi = `${entry.key.type}-${entry.key.group}-${entry.key.instance}`;
                
                const resourceId: ResourceIdentifier = {
                    type: entry.key.type,
                    group: entry.key.group,
                    instance: entry.key.instance,
                    modFile,
                    resourceIndex: resourceIndex++
                };
                
                if (!resourceMap.has(tgi)) {
                    resourceMap.set(tgi, []);
                }
                resourceMap.get(tgi)!.push(resourceId);
            }
        }
        
        return resourceMap;
    }
    
    /**
     * Detects exact duplicate resources (same TGI in multiple mods)
     */
    private static detectExactDuplicates(resourceMap: Map<string, ResourceIdentifier[]>): ResourceConflict[] {
        const conflicts: ResourceConflict[] = [];
        
        for (const [tgi, resources] of resourceMap) {
            if (resources.length > 1) {
                // Multiple mods have the same resource
                const affectedMods = [...new Set(resources.map(r => r.modFile))];
                
                if (affectedMods.length > 1) {
                    const severity = this.calculateDuplicateSeverity(resources[0].type);
                    
                    conflicts.push({
                        conflictType: ResourceConflictType.EXACT_DUPLICATE,
                        severity,
                        description: `Resource ${tgi} exists in ${affectedMods.length} mods`,
                        affectedMods,
                        affectedResources: resources,
                        resolution: this.getResolutionStrategy(ResourceConflictType.EXACT_DUPLICATE, severity),
                        autoFixAvailable: severity !== ConflictSeverity.CRITICAL
                    });
                }
            }
        }
        
        return conflicts;
    }
    
    /**
     * Detects override conflicts (multiple mods trying to override same base game resource)
     */
    private static detectOverrideConflicts(resourceMap: Map<string, ResourceIdentifier[]>): ResourceConflict[] {
        const conflicts: ResourceConflict[] = [];
        
        for (const [tgi, resources] of resourceMap) {
            if (resources.length > 1) {
                // Check if this looks like a base game override
                const isBaseGameOverride = this.isBaseGameOverride(resources[0]);
                
                if (isBaseGameOverride) {
                    const affectedMods = [...new Set(resources.map(r => r.modFile))];
                    
                    if (affectedMods.length > 1) {
                        conflicts.push({
                            conflictType: ResourceConflictType.OVERRIDE_CONFLICT,
                            severity: ConflictSeverity.HIGH,
                            description: `Multiple mods override base game resource ${tgi}`,
                            affectedMods,
                            affectedResources: resources,
                            resolution: this.getResolutionStrategy(ResourceConflictType.OVERRIDE_CONFLICT, ConflictSeverity.HIGH),
                            autoFixAvailable: false // User needs to choose which override to keep
                        });
                    }
                }
            }
        }
        
        return conflicts;
    }
    
    /**
     * Detects hash collisions (different content with same hash)
     */
    private static detectHashCollisions(resourceMap: Map<string, ResourceIdentifier[]>): ResourceConflict[] {
        const conflicts: ResourceConflict[] = [];
        
        // This would require comparing actual resource content
        // For now, we detect potential collisions based on patterns
        
        for (const [tgi, resources] of resourceMap) {
            if (resources.length > 1) {
                // Check for suspicious patterns that might indicate hash collisions
                const suspiciousPatterns = this.detectSuspiciousPatterns(resources);
                
                if (suspiciousPatterns.length > 0) {
                    const affectedMods = [...new Set(resources.map(r => r.modFile))];
                    
                    conflicts.push({
                        conflictType: ResourceConflictType.HASH_COLLISION,
                        severity: ConflictSeverity.MEDIUM,
                        description: `Potential hash collision detected for ${tgi}`,
                        affectedMods,
                        affectedResources: resources,
                        resolution: this.getResolutionStrategy(ResourceConflictType.HASH_COLLISION, ConflictSeverity.MEDIUM),
                        autoFixAvailable: false
                    });
                }
            }
        }
        
        return conflicts;
    }
    
    /**
     * Detects dependency conflicts
     */
    private static detectDependencyConflicts(
        resourceMap: Map<string, ResourceIdentifier[]>,
        modAnalyses: Map<string, ModContentAnalysis>
    ): ResourceConflict[] {
        const conflicts: ResourceConflict[] = [];
        
        // Check for missing dependencies
        for (const [modFile, analysis] of modAnalyses) {
            // Check if this mod has dependency requirements
            if (analysis.casContent.dependencySummary.missingCoreMods.length > 0) {
                conflicts.push({
                    conflictType: ResourceConflictType.DEPENDENCY_MISSING,
                    severity: ConflictSeverity.HIGH,
                    description: `${modFile} requires missing core mods: ${analysis.casContent.dependencySummary.missingCoreMods.join(', ')}`,
                    affectedMods: [modFile],
                    affectedResources: [],
                    resolution: this.getResolutionStrategy(ResourceConflictType.DEPENDENCY_MISSING, ConflictSeverity.HIGH),
                    autoFixAvailable: false
                });
            }
        }
        
        return conflicts;
    }
    
    /**
     * Calculates severity for duplicate resources based on type
     */
    private static calculateDuplicateSeverity(resourceType: number): ConflictSeverity {
        // CAS Parts - usually safe to have duplicates
        if (resourceType === 0x034AEECB) return ConflictSeverity.LOW;
        
        // Tuning resources - can cause gameplay issues
        if (resourceType === 0x62E94D38) return ConflictSeverity.HIGH;
        
        // Script resources - critical conflicts
        if (resourceType === 0x6017E896) return ConflictSeverity.CRITICAL;
        
        // Object definitions - medium severity
        if (resourceType === 0x319E4F1D) return ConflictSeverity.MEDIUM;
        
        // Default to medium
        return ConflictSeverity.MEDIUM;
    }
    
    /**
     * Determines if a resource is likely a base game override
     */
    private static isBaseGameOverride(resource: ResourceIdentifier): boolean {
        // Base game resources typically have group 0x80000000 or 0x00000000
        return resource.group === 0x80000000 || resource.group === 0x00000000;
    }
    
    /**
     * Detects suspicious patterns that might indicate hash collisions
     */
    private static detectSuspiciousPatterns(resources: ResourceIdentifier[]): string[] {
        const patterns: string[] = [];
        
        // Check for resources from different authors with same hash
        const modNames = resources.map(r => r.modFile.toLowerCase());
        const uniqueAuthors = new Set(modNames.map(name => {
            // Extract potential author from filename
            const parts = name.split(/[_\-\.]/);
            return parts[0];
        }));
        
        if (uniqueAuthors.size > 1 && resources.length > 1) {
            patterns.push('multiple_authors_same_hash');
        }
        
        return patterns;
    }
    
    /**
     * Gets resolution strategy for conflict type and severity
     */
    private static getResolutionStrategy(conflictType: ResourceConflictType, severity: ConflictSeverity): ConflictResolution {
        switch (conflictType) {
            case ResourceConflictType.EXACT_DUPLICATE:
                return {
                    strategy: ResolutionStrategy.KEEP_LATEST,
                    description: 'Keep the most recently modified version',
                    steps: [
                        'Compare file modification dates',
                        'Keep the newest version',
                        'Remove or disable older versions'
                    ],
                    automaticFix: severity !== ConflictSeverity.CRITICAL
                };
                
            case ResourceConflictType.OVERRIDE_CONFLICT:
                return {
                    strategy: ResolutionStrategy.USER_CHOICE,
                    description: 'User must choose which override to keep',
                    steps: [
                        'Review each mod\'s purpose',
                        'Choose the preferred override',
                        'Disable conflicting mods'
                    ],
                    automaticFix: false
                };
                
            case ResourceConflictType.DEPENDENCY_MISSING:
                return {
                    strategy: ResolutionStrategy.REMOVE_CONFLICTING,
                    description: 'Install missing dependencies or remove dependent mod',
                    steps: [
                        'Identify required core mods',
                        'Download and install dependencies',
                        'Or remove the dependent mod'
                    ],
                    automaticFix: false
                };
                
            default:
                return {
                    strategy: ResolutionStrategy.USER_CHOICE,
                    description: 'Manual resolution required',
                    steps: ['Review conflict details', 'Choose resolution manually'],
                    automaticFix: false
                };
        }
    }
}
