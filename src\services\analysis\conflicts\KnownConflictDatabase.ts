/**
 * Known Conflict Database
 * 
 * Database of known problematic mod combinations, version conflicts,
 * and common issues reported by the Sims 4 modding community.
 * 
 * Provides immediate value by detecting common conflicts without
 * complex analysis algorithms.
 */

import { ModContentAnalysis } from '../content/ContentAnalysisService';

/**
 * Known conflict between mods
 */
export interface KnownConflict {
    id: string;
    type: KnownConflictType;
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description: string;
    affectedMods: string[];
    detectionPattern: ConflictPattern;
    resolution: string;
    autoFixAvailable: boolean;
    reportedBy: string[];
    lastUpdated: string;
}

/**
 * Types of known conflicts
 */
export enum KnownConflictType {
    VERSION_INCOMPATIBLE = 'version_incompatible',
    AUTHOR_CONFLICT = 'author_conflict',
    CATEGORY_CONFLICT = 'category_conflict',
    DEPENDENCY_CONFLICT = 'dependency_conflict',
    GAME_VERSION_CONFLICT = 'game_version_conflict'
}

/**
 * <PERSON>tern for detecting conflicts
 */
export interface ConflictPattern {
    type: 'filename' | 'author' | 'content' | 'combination';
    patterns: string[];
    conditions: ConflictCondition[];
}

export interface ConflictCondition {
    field: string;
    operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex';
    value: string;
    caseSensitive?: boolean;
}

/**
 * Known Conflict Database
 */
export class KnownConflictDatabase {
    
    private static conflicts: KnownConflict[] = [
        // MCCC Version Conflicts
        {
            id: 'mccc_multiple_versions',
            type: KnownConflictType.VERSION_INCOMPATIBLE,
            severity: 'critical',
            title: 'Multiple MCCC Versions',
            description: 'Multiple versions of MC Command Center detected. Only one version should be installed.',
            affectedMods: [],
            detectionPattern: {
                type: 'filename',
                patterns: ['mc_', 'mccommand', 'mccc'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'mc_', caseSensitive: false },
                    { field: 'filename', operator: 'contains', value: 'command', caseSensitive: false }
                ]
            },
            resolution: 'Keep only the latest version of MCCC and remove all others',
            autoFixAvailable: false,
            reportedBy: ['community', 'deaderpool'],
            lastUpdated: '2025-01-27'
        },
        
        // UI Cheats Extension Conflicts
        {
            id: 'ui_cheats_multiple',
            type: KnownConflictType.VERSION_INCOMPATIBLE,
            severity: 'high',
            title: 'Multiple UI Cheats Extensions',
            description: 'Multiple UI Cheats Extension mods detected. These will conflict with each other.',
            affectedMods: [],
            detectionPattern: {
                type: 'filename',
                patterns: ['ui_cheat', 'uicheat', 'ui-cheat'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'ui', caseSensitive: false },
                    { field: 'filename', operator: 'contains', value: 'cheat', caseSensitive: false }
                ]
            },
            resolution: 'Keep only one UI Cheats Extension mod',
            autoFixAvailable: false,
            reportedBy: ['community', 'weerbesu'],
            lastUpdated: '2025-01-27'
        },
        
        // Basemental Conflicts
        {
            id: 'basemental_multiple',
            type: KnownConflictType.AUTHOR_CONFLICT,
            severity: 'high',
            title: 'Multiple Basemental Mods',
            description: 'Multiple Basemental mods detected. Some combinations may cause conflicts.',
            affectedMods: [],
            detectionPattern: {
                type: 'filename',
                patterns: ['basemental', 'basementalgangs', 'basementaldrugs'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'basemental', caseSensitive: false }
                ]
            },
            resolution: 'Ensure you have compatible versions of Basemental mods',
            autoFixAvailable: false,
            reportedBy: ['community', 'basemental'],
            lastUpdated: '2025-01-27'
        },
        
        // Pregnancy Overhaul Conflicts
        {
            id: 'pregnancy_overhaul_conflict',
            type: KnownConflictType.CATEGORY_CONFLICT,
            severity: 'high',
            title: 'Multiple Pregnancy Overhauls',
            description: 'Multiple pregnancy overhaul mods detected. These typically conflict with each other.',
            affectedMods: [],
            detectionPattern: {
                type: 'filename',
                patterns: ['pregnancy', 'pregnant', 'woohoo'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'pregnancy', caseSensitive: false }
                ]
            },
            resolution: 'Choose one pregnancy overhaul mod and remove others',
            autoFixAvailable: false,
            reportedBy: ['community'],
            lastUpdated: '2025-01-27'
        },
        
        // Wonderful/Wicked Whims Conflict
        {
            id: 'whims_conflict',
            type: KnownConflictType.CATEGORY_CONFLICT,
            severity: 'critical',
            title: 'Wonderful Whims + Wicked Whims',
            description: 'Wonderful Whims and Wicked Whims cannot be used together.',
            affectedMods: [],
            detectionPattern: {
                type: 'combination',
                patterns: ['wonderfulwhims', 'wickedwhims'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'wonderful', caseSensitive: false },
                    { field: 'filename', operator: 'contains', value: 'wicked', caseSensitive: false }
                ]
            },
            resolution: 'Choose either Wonderful Whims OR Wicked Whims, not both',
            autoFixAvailable: false,
            reportedBy: ['community', 'turbodriver'],
            lastUpdated: '2025-01-27'
        },
        
        // Lot51 Core Dependencies
        {
            id: 'lot51_core_missing',
            type: KnownConflictType.DEPENDENCY_CONFLICT,
            severity: 'high',
            title: 'Missing Lot51 Core Mod',
            description: 'Lot51 mods detected but core mod appears to be missing.',
            affectedMods: [],
            detectionPattern: {
                type: 'filename',
                patterns: ['lot51'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'lot51', caseSensitive: false }
                ]
            },
            resolution: 'Install Lot51 Core Library mod',
            autoFixAvailable: false,
            reportedBy: ['community', 'lot51'],
            lastUpdated: '2025-01-27'
        },
        
        // Outdated Game Version Conflicts
        {
            id: 'outdated_game_version',
            type: KnownConflictType.GAME_VERSION_CONFLICT,
            severity: 'medium',
            title: 'Potentially Outdated Mods',
            description: 'Some mods may be outdated for current game version.',
            affectedMods: [],
            detectionPattern: {
                type: 'filename',
                patterns: ['old', 'legacy', 'deprecated'],
                conditions: [
                    { field: 'filename', operator: 'contains', value: 'old', caseSensitive: false }
                ]
            },
            resolution: 'Check for updated versions of these mods',
            autoFixAvailable: false,
            reportedBy: ['community'],
            lastUpdated: '2025-01-27'
        }
    ];
    
    /**
     * Checks mod collection against known conflict database
     */
    public static async checkKnownConflicts(
        modAnalyses: Map<string, ModContentAnalysis>
    ): Promise<KnownConflict[]> {
        const detectedConflicts: KnownConflict[] = [];
        const modFiles = Array.from(modAnalyses.keys());
        
        console.log(`[KnownConflictDatabase] Checking ${modFiles.length} mods against known conflicts...`);
        
        for (const conflict of this.conflicts) {
            const matchingMods = this.findMatchingMods(modFiles, conflict.detectionPattern);
            
            if (this.isConflictDetected(matchingMods, conflict)) {
                const detectedConflict: KnownConflict = {
                    ...conflict,
                    affectedMods: matchingMods
                };
                detectedConflicts.push(detectedConflict);
            }
        }
        
        console.log(`[KnownConflictDatabase] Found ${detectedConflicts.length} known conflicts`);
        
        return detectedConflicts;
    }
    
    /**
     * Finds mods matching a conflict pattern
     */
    private static findMatchingMods(modFiles: string[], pattern: ConflictPattern): string[] {
        const matchingMods: string[] = [];
        
        for (const modFile of modFiles) {
            if (this.doesModMatchPattern(modFile, pattern)) {
                matchingMods.push(modFile);
            }
        }
        
        return matchingMods;
    }
    
    /**
     * Checks if a mod matches a conflict pattern
     */
    private static doesModMatchPattern(modFile: string, pattern: ConflictPattern): boolean {
        const fileName = modFile.toLowerCase();
        
        switch (pattern.type) {
            case 'filename':
                return pattern.patterns.some(p => fileName.includes(p.toLowerCase()));
                
            case 'author':
                // Extract potential author from filename
                const authorPart = fileName.split(/[_\-\.]/)[0];
                return pattern.patterns.some(p => authorPart.includes(p.toLowerCase()));
                
            case 'content':
                // This would require content analysis - for now, use filename
                return pattern.patterns.some(p => fileName.includes(p.toLowerCase()));
                
            case 'combination':
                // For combination patterns, we need to check if multiple patterns exist
                return pattern.patterns.some(p => fileName.includes(p.toLowerCase()));
                
            default:
                return false;
        }
    }
    
    /**
     * Determines if a conflict is actually detected based on matching mods
     */
    private static isConflictDetected(matchingMods: string[], conflict: KnownConflict): boolean {
        switch (conflict.type) {
            case KnownConflictType.VERSION_INCOMPATIBLE:
            case KnownConflictType.AUTHOR_CONFLICT:
            case KnownConflictType.CATEGORY_CONFLICT:
                // These require multiple matching mods
                return matchingMods.length > 1;
                
            case KnownConflictType.DEPENDENCY_CONFLICT:
                // Check if dependency mods are present but core is missing
                if (conflict.id === 'lot51_core_missing') {
                    const hasLot51Mods = matchingMods.length > 0;
                    const hasLot51Core = matchingMods.some(mod => 
                        mod.toLowerCase().includes('core') || mod.toLowerCase().includes('library')
                    );
                    return hasLot51Mods && !hasLot51Core;
                }
                return matchingMods.length > 0;
                
            case KnownConflictType.GAME_VERSION_CONFLICT:
                // Any matching mod indicates potential issue
                return matchingMods.length > 0;
                
            default:
                return matchingMods.length > 0;
        }
    }
    
    /**
     * Adds a new known conflict to the database
     */
    public static addKnownConflict(conflict: KnownConflict): void {
        this.conflicts.push(conflict);
    }
    
    /**
     * Gets all known conflicts
     */
    public static getAllKnownConflicts(): KnownConflict[] {
        return [...this.conflicts];
    }
    
    /**
     * Gets conflicts by type
     */
    public static getConflictsByType(type: KnownConflictType): KnownConflict[] {
        return this.conflicts.filter(c => c.type === type);
    }
    
    /**
     * Gets conflicts by severity
     */
    public static getConflictsBySeverity(severity: 'low' | 'medium' | 'high' | 'critical'): KnownConflict[] {
        return this.conflicts.filter(c => c.severity === severity);
    }
    
    /**
     * Updates the database with community-reported conflicts
     */
    public static async updateFromCommunity(): Promise<void> {
        // This would fetch updates from a community database
        // For now, it's a placeholder for future implementation
        console.log('[KnownConflictDatabase] Community updates not yet implemented');
    }
}
